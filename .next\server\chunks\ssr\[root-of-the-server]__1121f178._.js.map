{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport type * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,kPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,6SAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/app/page.tsx"], "sourcesContent": ["import * as motion from \"motion/react-client\";\nimport { Button } from \"@/components/ui/button\";\n\nexport default function Home() {\n  return (\n    <main className=\"flex flex-col gap-6 items-center justify-center min-h-screen\">\n      {/* <motion.div\n        className=\"size-28 bg-yellow-300 rounded-full\" */}\n      {/* // animate={{ x: -100 }}\n        // animate={{ y: 20 }}\n        // animate={{ rotate: 60 }}\n        // animate={{ scale: 2 }}\n        // animate={{ skewX: 20 }}\n      // /> */}\n\n      {/* Transition properties are duration, easing and delay */}\n\n      {/* <motion.div\n        className=\"size-28 bg-yellow-300 rounded-full\"\n        initial={{ x: 0 }}\n        animate={{ x: 100 }}\n        transition={{ duration: 2, ease: \"easeIn\", delay: 0.5 }}\n      /> */}\n\n      {/* Keyframes */}\n\n      {/* <motion.div\n        className=\"size-28 bg-yellow-300 rounded-ful\"\n        animate={{\n          // scale: [1, 2, 2, 3, 4, 3, 2, 1],\n          // rotate: [0, 0, 270, 270, 0],\n          borderRadius: [\"20%\", \"50%\", \"20%\", \"50%\"],\n        }}\n        transition={{ duration: 4, ease: \"easeIn\" }}\n      /> */}\n\n      <Button>Click me</Button>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;kBA+Bd,cAAA,6WAAC,qIAAM;sBAAC;;;;;;;;;;;AAGd", "debugId": null}}]}