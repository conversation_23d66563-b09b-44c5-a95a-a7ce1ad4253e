{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/app/page.tsx"], "sourcesContent": ["import * as motion from \"motion/react-client\";\n\nexport default function Home() {\n  return (\n    <main className=\"flex flex-col gap-6 items-center justify-center min-h-screen\">\n      {/* <motion.div\n        className=\"size-28 bg-yellow-300 rounded-full\" */}\n      {/* // animate={{ x: -100 }}\n        // animate={{ y: 20 }}\n        // animate={{ rotate: 60 }}\n        // animate={{ scale: 2 }}\n        // animate={{ skewX: 20 }}\n      // /> */}\n\n      {/* Transition properties are duration, easing and delay */}\n\n      {/* <motion.div\n        className=\"size-28 bg-yellow-300 rounded-full\"\n        initial={{ x: 0 }}\n        animate={{ x: 100 }}\n        transition={{ duration: 2, ease: \"easeIn\", delay: 0.5 }}\n      /> */}\n\n      {/* Keyframes */}\n\n      <motion.div\n        className=\"size-28 bg-yellow-300 rounded-ful\"\n        animate={{\n          // scale: [1, 2, 2, 3, 4, 3, 2, 1],\n          // rotate: [0, 0, 270, 270, 0],\n          borderRadius: [\"20%\", \"50%\", \"20%\", \"50%\"],\n        }}\n        transition={{ duration: 4, ease: \"easeIn\" }}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;kBAqBd,cAAA,6WAAC,kSAAU;YACT,WAAU;YACV,SAAS;gBACP,mCAAmC;gBACnC,+BAA+B;gBAC/B,cAAc;oBAAC;oBAAO;oBAAO;oBAAO;iBAAM;YAC5C;YACA,YAAY;gBAAE,UAAU;gBAAG,MAAM;YAAS;;;;;;;;;;;AAIlD", "debugId": null}}]}