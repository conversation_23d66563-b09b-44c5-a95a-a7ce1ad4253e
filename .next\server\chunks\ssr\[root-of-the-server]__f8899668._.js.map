{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/custom/mode-switcher.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ModeSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModeSwitcher() from the server but ModeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/custom/mode-switcher.tsx <module evaluation>\",\n    \"ModeSwitcher\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,uYAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/custom/mode-switcher.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ModeSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModeSwitcher() from the server but ModeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/custom/mode-switcher.tsx\",\n    \"ModeSwitcher\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,uYAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/providers/theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/providers/theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/hooks/use-metal-color.ts"], "sourcesContent": ["import { useTheme } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nexport const META_THEME_COLORS = {\r\n  light: \"#ffffff\",\r\n  dark: \"#0a0a0a\",\r\n};\r\n\r\nexport function useMetaColor() {\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const metaColor = React.useMemo(() => {\r\n    return resolvedTheme !== \"dark\"\r\n      ? META_THEME_COLORS.light\r\n      : META_THEME_COLORS.dark;\r\n  }, [resolvedTheme]);\r\n\r\n  const setMetaColor = React.useCallback((color: string) => {\r\n    document\r\n      .querySelector('meta[name=\"theme-color\"]')\r\n      ?.setAttribute(\"content\", color);\r\n  }, []);\r\n\r\n  return {\r\n    metaColor,\r\n    setMetaColor,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,oBAAoB;IAC/B,OAAO;IACP,MAAM;AACR;AAEO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,qQAAQ;IAElC,MAAM,YAAY,+UAAa,CAAC;QAC9B,OAAO,kBAAkB,SACrB,kBAAkB,KAAK,GACvB,kBAAkB,IAAI;IAC5B,GAAG;QAAC;KAAc;IAElB,MAAM,eAAe,mVAAiB,CAAC,CAAC;QACtC,SACG,aAAa,CAAC,6BACb,aAAa,WAAW;IAC9B,GAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ModeSwitcher } from \"@/components/custom/mode-switcher\";\nimport { ThemeProvider } from \"@/components/providers/theme-provider\";\nimport { META_THEME_COLORS } from \"@/hooks/use-metal-color\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <script\n          // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>\n          dangerouslySetInnerHTML={{\n            __html: `\n              try {\n                if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {\n                  document.querySelector('meta[name=\"theme-color\"]').setAttribute('content', '${META_THEME_COLORS.dark}')\n                }\n                if (localStorage.layout) {\n                  document.documentElement.classList.add('layout-' + localStorage.layout)\n                }\n              } catch (_) {}\n            `,\n          }}\n        />\n        <meta name=\"theme-color\" content={META_THEME_COLORS.light} />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <nav className=\"flex justify-end px-6 py-1\">\n            <ModeSwitcher />\n          </nav>\n          <main className=\"px-4 md:px-6 min-h-screen relative\">{children}</main>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AACA;AACA;;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,6WAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,6WAAC;;kCACC,6WAAC;wBACC,sEAAsE;wBACtE,yBAAyB;4BACvB,QAAQ,CAAC;;;8FAGyE,EAAE,mJAAiB,CAAC,IAAI,CAAC;;;;;;YAM3G,CAAC;wBACH;;;;;;kCAEF,6WAAC;wBAAK,MAAK;wBAAc,SAAS,mJAAiB,CAAC,KAAK;;;;;;;;;;;;0BAE3D,6WAAC;gBACC,WAAW,GAAG,oJAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,yJAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;0BAEpE,cAAA,6WAAC,8JAAa;oBACZ,WAAU;oBACV,cAAa;oBACb,YAAY;oBACZ,yBAAyB;;sCAEzB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,yJAAY;;;;;;;;;;sCAEf,6WAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;;;;;;AAKhE", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/node_modules/.pnpm/next%4015.5.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/node_modules/.pnpm/next-themes%400.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yJACA;AAEG,MAAM,WAAW,IAAA,uYAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yJACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/node_modules/.pnpm/next-themes%400.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qIACA;AAEG,MAAM,WAAW,IAAA,uYAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qIACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}