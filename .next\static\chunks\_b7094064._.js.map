{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,uOAAO,EAAC,IAAA,iMAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport type * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,qPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/hooks/use-metal-color.ts"], "sourcesContent": ["import { useTheme } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nexport const META_THEME_COLORS = {\r\n  light: \"#ffffff\",\r\n  dark: \"#0a0a0a\",\r\n};\r\n\r\nexport function useMetaColor() {\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const metaColor = React.useMemo(() => {\r\n    return resolvedTheme !== \"dark\"\r\n      ? META_THEME_COLORS.light\r\n      : META_THEME_COLORS.dark;\r\n  }, [resolvedTheme]);\r\n\r\n  const setMetaColor = React.useCallback((color: string) => {\r\n    document\r\n      .querySelector('meta[name=\"theme-color\"]')\r\n      ?.setAttribute(\"content\", color);\r\n  }, []);\r\n\r\n  return {\r\n    metaColor,\r\n    setMetaColor,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEO,MAAM,oBAAoB;IAC/B,OAAO;IACP,MAAM;AACR;AAEO,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,wQAAQ;IAElC,MAAM,YAAY,uSAAa;2CAAC;YAC9B,OAAO,kBAAkB,SACrB,kBAAkB,KAAK,GACvB,kBAAkB,IAAI;QAC5B;0CAAG;QAAC;KAAc;IAElB,MAAM,eAAe,2SAAiB;kDAAC,CAAC;gBACtC;aAAA,0BAAA,SACG,aAAa,CAAC,yCADjB,8CAAA,wBAEI,YAAY,CAAC,WAAW;QAC9B;iDAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF;GAnBgB;;QACY,wQAAQ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/custom/mode-switcher.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useMetaColor } from \"@/hooks/use-metal-color\";\r\n\r\nexport function ModeSwitcher() {\r\n  const { setTheme, resolvedTheme } = useTheme();\r\n  const { setMetaColor, metaColor } = useMetaColor();\r\n\r\n  React.useEffect(() => {\r\n    setMetaColor(metaColor);\r\n  }, [metaColor, setMetaColor]);\r\n\r\n  const toggleTheme = React.useCallback(() => {\r\n    setTheme(resolvedTheme === \"dark\" ? \"light\" : \"dark\");\r\n  }, [resolvedTheme, setTheme]);\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className=\"group/toggle extend-touch-target size-8\"\r\n      onClick={toggleTheme}\r\n      title=\"Toggle theme\"\r\n    >\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        width=\"24\"\r\n        height=\"24\"\r\n        viewBox=\"0 0 24 24\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        className=\"size-4.5\"\r\n      >\r\n        <title>Toggle theme</title>\r\n        <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\r\n        <path d=\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\" />\r\n        <path d=\"M12 3l0 18\" />\r\n        <path d=\"M12 9l4.65 -4.65\" />\r\n        <path d=\"M12 14.3l7.37 -7.37\" />\r\n        <path d=\"M12 19.6l8.85 -8.85\" />\r\n      </svg>\r\n      <span className=\"sr-only\">Toggle theme</span>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,IAAA,wQAAQ;IAC5C,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,IAAA,iJAAY;IAEhD,ySAAe;kCAAC;YACd,aAAa;QACf;iCAAG;QAAC;QAAW;KAAa;IAE5B,MAAM,cAAc,2SAAiB;iDAAC;YACpC,SAAS,kBAAkB,SAAS,UAAU;QAChD;gDAAG;QAAC;QAAe;KAAS;IAE5B,qBACE,4TAAC,wIAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAU;QACV,SAAS;QACT,OAAM;;0BAEN,4TAAC;gBACC,OAAM;gBACN,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;gBACf,WAAU;;kCAEV,4TAAC;kCAAM;;;;;;kCACP,4TAAC;wBAAK,QAAO;wBAAO,GAAE;wBAAgB,MAAK;;;;;;kCAC3C,4TAAC;wBAAK,GAAE;;;;;;kCACR,4TAAC;wBAAK,GAAE;;;;;;kCACR,4TAAC;wBAAK,GAAE;;;;;;kCACR,4TAAC;wBAAK,GAAE;;;;;;kCACR,4TAAC;wBAAK,GAAE;;;;;;;;;;;;0BAEV,4TAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA3CgB;;QACsB,wQAAQ;QACR,iJAAY;;;KAFlC", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/learn/motion/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\r\nimport type * as React from \"react\";\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,KAGoB;QAHpB,EAC5B,QAAQ,EACR,GAAG,OAC6C,GAHpB;IAI5B,qBAAO,4TAAC,6QAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}]}