import * as motion from "motion/react-client";
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <main className="flex flex-col gap-6 items-center justify-center min-h-screen">
      {/* <motion.div
        className="size-28 bg-yellow-300 rounded-full" */}
      {/* // animate={{ x: -100 }}
        // animate={{ y: 20 }}
        // animate={{ rotate: 60 }}
        // animate={{ scale: 2 }}
        // animate={{ skewX: 20 }}
      // /> */}

      {/* Transition properties are duration, easing and delay */}

      {/* <motion.div
        className="size-28 bg-yellow-300 rounded-full"
        initial={{ x: 0 }}
        animate={{ x: 100 }}
        transition={{ duration: 2, ease: "easeIn", delay: 0.5 }}
      /> */}

      {/* Keyframes */}

      {/* <motion.div
        className="size-28 bg-yellow-300 rounded-ful"
        animate={{
          // scale: [1, 2, 2, 3, 4, 3, 2, 1],
          // rotate: [0, 0, 270, 270, 0],
          borderRadius: ["20%", "50%", "20%", "50%"],
        }}
        transition={{ duration: 4, ease: "easeIn" }}
      /> */}

      <Button>Click me</Button>
    </main>
  );
}
